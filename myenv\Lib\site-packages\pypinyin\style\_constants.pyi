from typing import Any, List, Dict, Text


_INITIALS = ...  # type: List[Text]

_INITIALS_NOT_STRICT = ...  # type: List[Text]

_FINALS = ...  # type: List[Text]


PHONETIC_SYMBOL_DICT = ...  # type: Dict[Text, Text]
PHONETIC_SYMBOL_DICT_KEY_LENGTH_NOT_ONE = ...  # type: Dict[Text, Text]

RE_PHONETIC_SYMBOL = ...  # type : Any


RE_TONE2 = ...  # type : Any

RE_TONE3 = ...  # type : Any


RE_NUMBER = ...  # type: Any
