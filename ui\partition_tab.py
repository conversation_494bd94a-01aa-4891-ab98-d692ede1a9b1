from PyQt5.QtWidgets import QTabWidget, QWidget, QInputDialog, QMessageBox, QPushButton, QHBoxLayout

class PartitionTab(QTabWidget):
    def __init__(self, data_manager, app_panel_factory, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.app_panel_factory = app_panel_factory  # 工厂函数，生成每个分区的面板
        self.setTabsClosable(True)
        self.tabCloseRequested.connect(self.close_partition)
        self.init_tabs()

    def init_tabs(self):
        self.clear()
        partitions = self.data_manager.config.get('partitions', {'默认': {}})
        for name, pdata in partitions.items():
            self.add_partition_widget(name, pdata)
        if self.count() == 0:
            self.add_partition()

    def add_partition(self):
        name, ok = QInputDialog.getText(self, '新建分区', '分区名称:')
        if ok and name and name not in self.data_manager.config.get('partitions', {}):
            self.data_manager.config.setdefault('partitions', {})[name] = {}
            self.add_partition_widget(name, {})
            self.data_manager.save_config()

    def add_partition_widget(self, name, pdata):
        panel = self.app_panel_factory(name, pdata)
        self.addTab(panel, name)

    def close_partition(self, idx):
        name = self.tabText(idx)
        if self.count() == 1:
            QMessageBox.warning(self, '提示', '至少保留一个分区！')
            return
        self.removeTab(idx)
        if name in self.data_manager.config.get('partitions', {}):
            del self.data_manager.config['partitions'][name]
        self.data_manager.save_config() 