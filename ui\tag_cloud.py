from PyQt5.QtWidgets import QWidget
from PyQt5.QtGui import Q<PERSON><PERSON>ter, QFont, QColor
from PyQt5.QtCore import Qt, QRect

class TagCloudWidget(QWidget):
    def __init__(self, get_tag_counts, on_tag_clicked, get_current_tag, parent=None):
        super().__init__(parent)
        self.get_tag_counts = get_tag_counts
        self.on_tag_clicked = on_tag_clicked
        self.get_current_tag = get_current_tag
        self.setMinimumHeight(40)
        self.setMouseTracking(True)
        self._tag_rects = []

    def paintEvent(self, event):
        painter = QPainter(self)
        tag_counts = self.get_tag_counts()
        if not tag_counts:
            return
        max_count = max(tag_counts.values()) if tag_counts else 1
        if max_count == 0:
            return
        min_font = 10
        max_font = 24
        x, y = 10, 10
        self._tag_rects = []
        for tag, count in tag_counts.items():
            font_size = min_font + int((count / max_count) * (max_font - min_font))
            font = QFont()
            font.setPointSize(font_size)
            painter.setFont(font)
            color = QColor(30, 144, 255) if tag == self.get_current_tag() else QColor(80, 80, 80)
            painter.setPen(color)
            tag_text = f'{tag}({count})'
            rect = painter.boundingRect(QRect(x, y, 200, 40), Qt.AlignLeft, tag_text)
            if x + rect.width() > self.width() - 10:
                x = 10
                y += rect.height() + 5
                rect = painter.boundingRect(QRect(x, y, 200, 40), Qt.AlignLeft, tag_text)
            painter.drawText(rect, Qt.AlignLeft, tag_text)
            self._tag_rects.append((rect, tag))
            x += rect.width() + 20

    def mousePressEvent(self, event):
        pos = event.pos()
        for rect, tag in self._tag_rects:
            if rect.contains(pos):
                self.on_tag_clicked(tag)
                self.update()
                break 