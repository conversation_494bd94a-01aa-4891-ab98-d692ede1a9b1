import ctypes
import os
import hashlib
import json
import time
from typing import Optional, List, Dict
from PyQt5.QtGui import QPixmap
from PyQt5.QtCore import QThread, pyqtSignal

ICON_CACHE_DIR = 'icon_cache'
ICON_METADATA_FILE = os.path.join(ICON_CACHE_DIR, 'icon_metadata.json')

if not os.path.exists(ICON_CACHE_DIR):
    os.makedirs(ICON_CACHE_DIR)

def exe_icon_cache_path(exe_path):
    h = hashlib.md5(exe_path.encode('utf-8')).hexdigest()
    return os.path.join(ICON_CACHE_DIR, f'{h}.png')

class IconCache:
    """Enhanced icon cache with metadata tracking"""

    def __init__(self):
        self.metadata: Dict[str, Dict] = {}
        self.load_metadata()

    def load_metadata(self):
        """Load icon cache metadata"""
        try:
            if os.path.exists(ICON_METADATA_FILE):
                with open(ICON_METADATA_FILE, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
        except (json.JSONDecodeError, IOError):
            self.metadata = {}

    def save_metadata(self):
        """Save icon cache metadata"""
        try:
            with open(ICON_METADATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
        except IOError:
            pass

    def get_cache_info(self, exe_path: str) -> Optional[Dict]:
        """Get cache information for an executable"""
        return self.metadata.get(exe_path)

    def update_cache_info(self, exe_path: str, info: Dict):
        """Update cache information for an executable"""
        self.metadata[exe_path] = info
        self.save_metadata()

# Global icon cache instance
_icon_cache = IconCache()

def get_file_version_info(exe_path: str) -> Dict[str, str]:
    """Extract version information from executable"""
    try:
        import win32api
        info = win32api.GetFileVersionInfo(exe_path, "\\")
        ms = info['FileVersionMS']
        ls = info['FileVersionLS']
        version = f"{win32api.HIWORD(ms)}.{win32api.LOWORD(ms)}.{win32api.HIWORD(ls)}.{win32api.LOWORD(ls)}"

        # Get string file info
        try:
            translation_info = win32api.GetFileVersionInfo(exe_path, '\\VarFileInfo\\Translation')
            if translation_info and len(translation_info) > 0:
                lang, codepage = translation_info[0][0], translation_info[0][1]
                string_file_info = {}
                for name in ['CompanyName', 'FileDescription', 'FileVersion', 'ProductName', 'ProductVersion']:
                    try:
                        value = win32api.GetFileVersionInfo(exe_path, f'\\StringFileInfo\\{lang:04x}{codepage:04x}\\{name}')
                        if value:
                            string_file_info[name] = value
                    except:
                        pass

                return {
                    'version': version,
                    'description': string_file_info.get('FileDescription', ''),
                    'company': string_file_info.get('CompanyName', ''),
                    'product': string_file_info.get('ProductName', '')
                }
        except:
            pass

        return {'version': version}
    except:
        return {}

def extract_icons_from_exe(exe_path: str, icon_count: int = 10, sizes: Optional[List[int]] = None) -> List[QPixmap]:
    """
    Enhanced icon extraction with multiple sizes and caching
    """
    if sizes is None:
        sizes = [16, 32, 48, 64, 128, 256]

    cache_file = exe_icon_cache_path(exe_path)
    cache_info = _icon_cache.get_cache_info(exe_path)

    # Check if cache is valid
    try:
        file_mtime = os.path.getmtime(exe_path)
        if cache_info and cache_info.get('mtime') == file_mtime and os.path.exists(cache_file):
            pixmap = QPixmap(cache_file)
            if not pixmap.isNull():
                return [pixmap]
    except OSError:
        pass

    # Extract icons
    try:
        ExtractIconEx = ctypes.windll.shell32.ExtractIconExW
        large = (ctypes.c_void_p * icon_count)()
        small = (ctypes.c_void_p * icon_count)()
        count = ExtractIconEx(exe_path, 0, large, small, icon_count)

        pixmaps = []
        best_pixmap = None

        for i in range(count):
            hicon = large[i] or small[i]
            if hicon:
                try:
                    # Create QPixmap from HICON
                    pixmap = QPixmap.fromWinHICON(hicon)
                    if pixmap and not pixmap.isNull():
                        pixmaps.append(pixmap)
                        if i == 0:  # Use first icon as best
                            best_pixmap = pixmap
                except:
                    pass
                finally:
                    ctypes.windll.user32.DestroyIcon(hicon)

        # Cache the best icon
        if best_pixmap:
            best_pixmap.save(cache_file, 'PNG')

            # Update cache metadata
            try:
                file_mtime = os.path.getmtime(exe_path)
                version_info = get_file_version_info(exe_path)
                cache_info = {
                    'mtime': file_mtime,
                    'cached_time': time.time(),
                    'icon_count': len(pixmaps),
                    **version_info
                }
                _icon_cache.update_cache_info(exe_path, cache_info)
            except:
                pass

        return pixmaps
    except Exception as e:
        print(f"Error extracting icons from {exe_path}: {e}")
        return []

class IconLoaderThread(QThread):
    """Enhanced icon loader with metadata extraction"""
    iconLoaded = pyqtSignal(str, QPixmap)
    metadataLoaded = pyqtSignal(str, dict)

    def __init__(self, exe_path: str, load_metadata: bool = False):
        super().__init__()
        self.exe_path = exe_path
        self.load_metadata = load_metadata

    def run(self):
        try:
            pixmaps = extract_icons_from_exe(self.exe_path)
            if pixmaps:
                self.iconLoaded.emit(self.exe_path, pixmaps[0])

            if self.load_metadata:
                metadata = get_file_version_info(self.exe_path)
                if metadata:
                    self.metadataLoaded.emit(self.exe_path, metadata)
        except Exception as e:
            print(f"Error loading icon for {self.exe_path}: {e}")

class BatchIconLoader(QThread):
    """Batch icon loader for multiple applications"""
    iconLoaded = pyqtSignal(str, QPixmap)
    progressUpdated = pyqtSignal(int, int)  # current, total
    finished = pyqtSignal()

    def __init__(self, exe_paths: List[str]):
        super().__init__()
        self.exe_paths = exe_paths
        self.should_stop = False

    def stop(self):
        self.should_stop = True

    def run(self):
        total = len(self.exe_paths)
        for i, exe_path in enumerate(self.exe_paths):
            if self.should_stop:
                break

            try:
                pixmaps = extract_icons_from_exe(exe_path)
                if pixmaps:
                    self.iconLoaded.emit(exe_path, pixmaps[0])
            except Exception as e:
                print(f"Error loading icon for {exe_path}: {e}")

            self.progressUpdated.emit(i + 1, total)

        self.finished.emit()

# Utility functions
def clear_icon_cache():
    """Clear all cached icons"""
    try:
        for file in os.listdir(ICON_CACHE_DIR):
            if file.endswith('.png'):
                os.remove(os.path.join(ICON_CACHE_DIR, file))

        # Clear metadata
        global _icon_cache
        _icon_cache.metadata = {}
        _icon_cache.save_metadata()
        return True
    except Exception as e:
        print(f"Error clearing icon cache: {e}")
        return False

def get_cache_size() -> int:
    """Get total size of icon cache in bytes"""
    try:
        total_size = 0
        for file in os.listdir(ICON_CACHE_DIR):
            file_path = os.path.join(ICON_CACHE_DIR, file)
            if os.path.isfile(file_path):
                total_size += os.path.getsize(file_path)
        return total_size
    except:
        return 0

def cleanup_orphaned_cache():
    """Remove cached icons for non-existent executables"""
    try:
        removed_count = 0
        for exe_path in list(_icon_cache.metadata.keys()):
            if not os.path.exists(exe_path):
                cache_file = exe_icon_cache_path(exe_path)
                if os.path.exists(cache_file):
                    os.remove(cache_file)
                del _icon_cache.metadata[exe_path]
                removed_count += 1

        if removed_count > 0:
            _icon_cache.save_metadata()

        return removed_count
    except Exception as e:
        print(f"Error cleaning up cache: {e}")
        return 0