import ctypes
import os
import hashlib
from PyQt5.QtGui import QPixmap
from PyQt5.QtWinExtras import QtWin
from PyQt5.QtCore import QThread, pyqtSignal

ICON_CACHE_DIR = 'icon_cache'

if not os.path.exists(ICON_CACHE_DIR):
    os.makedirs(ICON_CACHE_DIR)

def exe_icon_cache_path(exe_path):
    h = hashlib.md5(exe_path.encode('utf-8')).hexdigest()
    return os.path.join(ICON_CACHE_DIR, f'{h}.png')

def extract_icons_from_exe(exe_path, icon_count=10):
    """
    优先从缓存加载，否则用ExtractIconExW提取EXE中的多分辨率图标，返回QPixmap列表。
    """
    cache_file = exe_icon_cache_path(exe_path)
    if os.path.exists(cache_file):
        pixmap = QPixmap(cache_file)
        if not pixmap.isNull():
            return [pixmap]
    # 提取
    ExtractIconEx = ctypes.windll.shell32.ExtractIconExW
    large = (ctypes.c_void_p * icon_count)()
    small = (ctypes.c_void_p * icon_count)()
    count = ExtractIconEx(exe_path, 0, large, small, icon_count)
    pixmaps = []
    for i in range(count):
        hicon = large[i] or small[i]
        if hicon:
            pixmap = QtWin.fromHICON(int(hicon))
            if pixmap:
                pixmaps.append(pixmap)
                # 只缓存第一个
                if i == 0:
                    pixmap.save(cache_file, 'PNG')
            ctypes.windll.user32.DestroyIcon(hicon)
    return pixmaps

class IconLoaderThread(QThread):
    iconLoaded = pyqtSignal(str, QPixmap)
    def __init__(self, exe_path):
        super().__init__()
        self.exe_path = exe_path
    def run(self):
        pixmaps = extract_icons_from_exe(self.exe_path)
        if pixmaps:
            self.iconLoaded.emit(self.exe_path, pixmaps[0]) 