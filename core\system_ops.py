import os
import subprocess
from PyQt5.QtWidgets import QMessageBox

def shutdown(parent=None):
    reply = QMessageBox.question(parent, '确认关机', '确定要关机吗？', QMessageBox.Yes | QMessageBox.No)
    if reply == QMessageBox.Yes:
        os.system('shutdown /s /t 0')

def reboot(parent=None):
    reply = QMessageBox.question(parent, '确认重启', '确定要重启吗？', QMessageBox.Yes | QMessageBox.No)
    if reply == QMessageBox.Yes:
        os.system('shutdown /r /t 0')

def open_settings(parent=None):
    try:
        os.system('start ms-settings:')
    except Exception as e:
        QMessageBox.warning(parent, '打开失败', str(e))

def open_uwp(parent=None):
    try:
        os.system('explorer shell:AppsFolder')
    except Exception as e:
        QMessageBox.warning(parent, '打开失败', str(e)) 