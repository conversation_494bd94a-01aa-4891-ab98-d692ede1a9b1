from PyQt5.QtWidgets import QWidget, QVBoxLayout, QToolBar, QPushButton
from ui.partition_tab import PartitionTab
from core.system_ops import shutdown, reboot, open_settings, open_uwp

class MainWindowUI(QWidget):
    def __init__(self, data_manager, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.data_manager = data_manager
        self.setWindowTitle('Windesktop 启动器（多分区）')
        self.resize(900, 600)
        self.is_dark = False
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        # 已彻底移除显示器相关UI

        # 系统操作工具栏
        toolbar = QToolBar('系统操作', self)
        btn_shutdown = QPushButton('关机', self)
        btn_shutdown.clicked.connect(lambda: shutdown(self))
        toolbar.addWidget(btn_shutdown)
        btn_reboot = QPushButton('重启', self)
        btn_reboot.clicked.connect(lambda: reboot(self))
        toolbar.addWidget(btn_reboot)
        btn_settings = QPushButton('系统设置', self)
        btn_settings.clicked.connect(lambda: open_settings(self))
        toolbar.addWidget(btn_settings)
        btn_uwp = QPushButton('UWP应用', self)
        btn_uwp.clicked.connect(lambda: open_uwp(self))
        toolbar.addWidget(btn_uwp)
        # 主题切换和帮助按钮
        self.theme_btn = QPushButton('深色主题', self)
        self.theme_btn.clicked.connect(self.toggle_theme)
        toolbar.addWidget(self.theme_btn)
        help_btn = QPushButton('帮助', self)
        help_btn.clicked.connect(self.show_help)
        toolbar.addWidget(help_btn)
        layout.addWidget(toolbar)

        # 分区Tab
        self.partition_tab = PartitionTab(self.data_manager, self.create_app_panel, self)
        layout.addWidget(self.partition_tab)
        self.setLayout(layout)

    def create_app_panel(self, name, pdata):
        from ui.app_list_panel import AppListPanel
        return AppListPanel(pdata, self.data_manager)

    def toggle_theme(self):
        self.is_dark = not self.is_dark
        # TODO: 切换主题样式
        self.theme_btn.setText('浅色主题' if self.is_dark else '深色主题')

    def show_help(self):
        from PyQt5.QtWidgets import QMessageBox
        msg = (
            'Windesktop 启动器帮助\n\n'
            '主要功能：\n'
            '1. 拖拽EXE生成快捷方式，自动提取多分辨率图标\n'
            '2. 多级分类、智能分组、标签云、模糊/拼音搜索\n'
            '3. 拖拽排序、中键删除、跨分类移动\n'
            '4. 多分区、多显示器、系统集成、备份恢复\n'
            '\n快捷键：\n'
            'Ctrl+F：聚焦搜索框\n'
            'Ctrl+T：切换深色/浅色主题\n'
            'F1：显示帮助\n'
            'Tab：切换分区\n'
            'Esc：关闭弹窗\n'
        )
        QMessageBox.information(self, '帮助', msg) 