from PyQt5.QtWidgets import <PERSON><PERSON>idget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem, QTreeWidget, QTreeWidgetItem, QLabel, QLineEdit, QMessageBox
from PyQt5.QtCore import Qt, QMimeData
from PyQt5.QtGui import QIcon
from ui.tag_cloud import TagCloudWidget
from core.icon_utils import IconLoaderThread
from pypinyin import lazy_pinyin
import os
import pythoncom
import win32com.client

def resolve_link(path):
    # 解析.lnk快捷方式为真实exe路径
    try:
        pythoncom.CoInitialize()
        shell = win32com.client.Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        target = shortcut.Targetpath
        if target and os.path.exists(target):
            return target
    except Exception:
        pass
    return None

class AppListPanel(QWidget):
    def __init__(self, partition_data, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.data = partition_data.get('data', {"默认": []})
        self.launch_count = partition_data.get('launch_count', {})
        self.current_category = "默认"
        self.exe_items = {}
        self.search_text = ''
        self.tag_cloud_selected = None
        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout(self)
        # 搜索框
        search_layout = QHBoxLayout()
        self.search_edit = QLineEdit(self)
        self.search_edit.setPlaceholderText('搜索（支持拼音/模糊）...')
        self.search_edit.textChanged.connect(self.on_search)
        search_layout.addWidget(QLabel('搜索:'))
        search_layout.addWidget(self.search_edit)
        main_layout.addLayout(search_layout)
        # 标签云
        self.tag_cloud = TagCloudWidget(self.get_tag_counts, self.on_tag_clicked, self.get_current_tag, self)
        main_layout.addWidget(self.tag_cloud)
        # 分类树和应用列表
        hbox = QHBoxLayout()
        self.tree = QTreeWidget(self)
        self.tree.setHeaderLabel('分类')
        self.tree.itemClicked.connect(self.on_category_selected)
        hbox.addWidget(self.tree)
        vbox = QVBoxLayout()
        self.label = QLabel('拖拽EXE或快捷方式到此窗口生成快捷方式', self)
        vbox.addWidget(self.label)
        self.listWidget = QListWidget(self)
        self.listWidget.setDragDropMode(QListWidget.InternalMove)
        self.listWidget.itemDoubleClicked.connect(self.run_app)
        vbox.addWidget(self.listWidget)
        hbox.addLayout(vbox)
        main_layout.addLayout(hbox)
        self.setLayout(main_layout)
        self.populate_tree()
        self.refresh_app_list()

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            for url in event.mimeData().urls():
                path = url.toLocalFile()
                if path.lower().endswith('.exe') or path.lower().endswith('.lnk'):
                    event.accept()
                    return
        event.ignore()

    def dropEvent(self, event):
        for url in event.mimeData().urls():
            path = url.toLocalFile()
            exe_path = None
            if path.lower().endswith('.exe'):
                exe_path = path
            elif path.lower().endswith('.lnk'):
                exe_path = resolve_link(path)
            if exe_path and exe_path.lower().endswith('.exe'):
                self.add_exe_item(exe_path)
                if exe_path not in self.data.setdefault(self.current_category, []):
                    self.data[self.current_category].append(exe_path)
            else:
                QMessageBox.warning(self, '添加失败', f'无法识别或解析：{os.path.basename(path)}')

    def get_tag_counts(self):
        return {k: len(v) for k, v in self.data.items()}

    def on_tag_clicked(self, tag):
        self.current_category = tag
        self.tag_cloud_selected = tag
        self.refresh_app_list()
        self.tag_cloud.update()
        items = self.tree.findItems(tag, Qt.MatchRecursive | Qt.MatchExactly)
        if items:
            self.tree.setCurrentItem(items[0])

    def get_current_tag(self):
        return self.current_category

    def on_search(self, text):
        self.search_text = text.strip()
        self.refresh_app_list()

    def match_search(self, exe_path):
        if not self.search_text:
            return True
        name = exe_path.lower()
        py = ''.join([s[0] for s in lazy_pinyin(name)])
        py_full = ''.join(lazy_pinyin(name))
        st = self.search_text.lower()
        return (st in name or st in py or st in py_full)

    def populate_tree(self):
        self.tree.clear()
        for k in self.data.keys():
            self.tree.addTopLevelItem(QTreeWidgetItem([k]))
        self.tree.expandAll()

    def on_category_selected(self, item, col):
        self.current_category = item.text(0)
        self.tag_cloud_selected = self.current_category
        self.refresh_app_list()
        self.tag_cloud.update()

    def refresh_app_list(self):
        self.listWidget.clear()
        self.exe_items.clear()
        exes = self.data.get(self.current_category, [])
        for exe_path in exes:
            self.add_exe_item(exe_path, refresh=True)

    def add_exe_item(self, exe_path, refresh=False):
        if exe_path in self.exe_items:
            return
        item = QListWidgetItem(exe_path.split(os.sep)[-1])
        item.setData(Qt.UserRole, exe_path)
        self.listWidget.addItem(item)
        self.exe_items[exe_path] = item
        def set_icon(path, pixmap):
            if path in self.exe_items:
                self.exe_items[path].setIcon(QIcon(pixmap))
        thread = IconLoaderThread(exe_path)
        thread.iconLoaded.connect(set_icon)
        thread.start()
        if not hasattr(self, '_icon_threads'):
            self._icon_threads = []
        self._icon_threads.append(thread)
        if not refresh and exe_path not in self.data.setdefault(self.current_category, []):
            self.data[self.current_category].append(exe_path)

    def run_app(self, item):
        exe_path = item.data(Qt.UserRole)
        try:
            import subprocess
            subprocess.Popen([exe_path])
            self.launch_count[exe_path] = self.launch_count.get(exe_path, 0) + 1
        except Exception as e:
            QMessageBox.warning(self, '启动失败', str(e)) 