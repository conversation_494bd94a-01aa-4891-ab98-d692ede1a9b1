from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
                             QTreeWidget, QTreeWidgetItem, QLabel, QLineEdit, QMessageBox,
                             QMenu, QAction, QProgressBar, QFrame, QSplitter, QApplication,
                             QToolTip, QRubberBand)
from PyQt5.QtCore import Qt, QMimeData, QTimer, QPoint, QRect, pyqtSignal
from PyQt5.QtGui import QIcon, QPainter, QColor, QBrush, QPen, QDrag, QPixmap, QCursor
from ui.tag_cloud import TagCloudWidget
from core.icon_utils import IconLoaderThread, BatchIconLoader
from core.data_manager import DataManager
from pypinyin import lazy_pinyin
import os
import pythoncom
import win32com.client
import subprocess
from typing import List, Optional

def resolve_link(path):
    # 解析.lnk快捷方式为真实exe路径
    try:
        pythoncom.CoInitialize()
        shell = win32com.client.Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        target = shortcut.Targetpath
        if target and os.path.exists(target):
            return target
    except Exception:
        pass
    return None

class EnhancedListWidget(QListWidget):
    """Enhanced list widget with better drag-and-drop support"""

    # Custom signals
    itemsDropped = pyqtSignal(list, str)  # dropped items, target category
    itemsMoved = pyqtSignal(list, str, str)  # moved items, from category, to category
    contextMenuRequested = pyqtSignal(QPoint, list)  # position, selected items

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setDragDropMode(QListWidget.DragDrop)
        self.setDefaultDropAction(Qt.DropAction.MoveAction)
        self.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)

        # Visual feedback
        self.drag_indicator = None
        self.drop_indicator_rect = QRect()
        self.is_drag_active = False

        # Context menu
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_context_menu)

        # Drag and drop state
        self.drag_start_position = QPoint()
        self.dragged_items = []

    def _show_context_menu(self, position: QPoint):
        """Show context menu for selected items"""
        selected_items = self.selectedItems()
        if selected_items:
            self.contextMenuRequested.emit(position, selected_items)

    def mousePressEvent(self, event):
        """Handle mouse press for drag initiation"""
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.pos()
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """Handle mouse move for drag initiation"""
        if not (event.buttons() & Qt.LeftButton):
            return

        if ((event.pos() - self.drag_start_position).manhattanLength() <
            QApplication.startDragDistance()):
            return

        # Start drag operation
        selected_items = self.selectedItems()
        if selected_items:
            self._start_drag(selected_items)

    def _start_drag(self, items: List[QListWidgetItem]):
        """Start drag operation with visual feedback"""
        self.dragged_items = items
        self.is_drag_active = True

        # Create drag object
        drag = QDrag(self)
        mime_data = QMimeData()

        # Store item data
        item_paths = []
        for item in items:
            path = item.data(Qt.UserRole)
            if path:
                item_paths.append(path)

        mime_data.setText('\n'.join(item_paths))
        drag.setMimeData(mime_data)

        # Create drag pixmap
        if len(items) == 1:
            drag_pixmap = self._create_drag_pixmap(items[0])
        else:
            drag_pixmap = self._create_multi_drag_pixmap(items)

        drag.setPixmap(drag_pixmap)
        drag.setHotSpot(QPoint(drag_pixmap.width() // 2, drag_pixmap.height() // 2))

        # Execute drag
        drop_action = drag.exec_(Qt.MoveAction | Qt.CopyAction, Qt.MoveAction)

        self.is_drag_active = False
        self.dragged_items = []

    def _create_drag_pixmap(self, item: QListWidgetItem) -> QPixmap:
        """Create drag pixmap for single item"""
        rect = self.visualItemRect(item)
        pixmap = QPixmap(rect.size())
        pixmap.fill(Qt.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        # Draw item background
        painter.fillRect(pixmap.rect(), QColor(100, 150, 255, 180))

        # Draw icon and text
        icon = item.icon()
        if not icon.isNull():
            icon_rect = QRect(4, 4, 32, 32)
            icon.paint(painter, icon_rect)

        painter.setPen(Qt.white)
        text_rect = QRect(40, 4, pixmap.width() - 44, pixmap.height() - 8)
        painter.drawText(text_rect, Qt.AlignLeft | Qt.AlignVCenter, item.text())

        painter.end()
        return pixmap

    def _create_multi_drag_pixmap(self, items: List[QListWidgetItem]) -> QPixmap:
        """Create drag pixmap for multiple items"""
        pixmap = QPixmap(120, 80)
        pixmap.fill(Qt.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        # Draw stacked rectangles
        for i in range(min(3, len(items))):
            offset = i * 3
            rect = QRect(offset, offset, 100, 60)
            painter.fillRect(rect, QColor(100, 150, 255, 150 - i * 30))
            painter.setPen(QColor(50, 100, 200))
            painter.drawRect(rect)

        # Draw count
        painter.setPen(Qt.white)
        painter.setFont(painter.font())
        count_text = f"{len(items)} items"
        painter.drawText(pixmap.rect(), Qt.AlignCenter, count_text)

        painter.end()
        return pixmap

    def dragEnterEvent(self, event):
        """Handle drag enter with visual feedback"""
        if event.mimeData().hasText() or event.mimeData().hasUrls():
            event.acceptProposedAction()
            self.update()
        else:
            event.ignore()

    def dragMoveEvent(self, event):
        """Handle drag move with visual feedback"""
        if event.mimeData().hasText() or event.mimeData().hasUrls():
            event.acceptProposedAction()

            # Update drop indicator
            item = self.itemAt(event.pos())
            if item:
                self.drop_indicator_rect = self.visualItemRect(item)
            else:
                self.drop_indicator_rect = QRect()

            self.update()
        else:
            event.ignore()

    def dragLeaveEvent(self, event):
        """Handle drag leave"""
        self.drop_indicator_rect = QRect()
        self.update()

    def dropEvent(self, event):
        """Handle drop with enhanced processing"""
        self.drop_indicator_rect = QRect()

        if event.mimeData().hasUrls():
            # External file drop
            urls = event.mimeData().urls()
            file_paths = [url.toLocalFile() for url in urls]
            self.itemsDropped.emit(file_paths, "")
            event.acceptProposedAction()
        elif event.mimeData().hasText():
            # Internal item move
            item_paths = event.mimeData().text().split('\n')
            target_item = self.itemAt(event.pos())
            target_category = ""  # Will be determined by parent
            self.itemsMoved.emit(item_paths, "", target_category)
            event.acceptProposedAction()

        self.update()

    def paintEvent(self, event):
        """Custom paint with drag indicators"""
        super().paintEvent(event)

        if not self.drop_indicator_rect.isEmpty():
            painter = QPainter(self.viewport())
            painter.setRenderHint(QPainter.Antialiasing)

            # Draw drop indicator
            painter.setPen(QPen(QColor(100, 150, 255), 2))
            painter.setBrush(QBrush(QColor(100, 150, 255, 50)))
            painter.drawRect(self.drop_indicator_rect)

            painter.end()

class AppListPanel(QWidget):
    """Enhanced application list panel with improved drag-and-drop and categorization"""

    # Custom signals
    appLaunched = pyqtSignal(str)  # app path
    appAdded = pyqtSignal(str, str)  # app path, category
    appRemoved = pyqtSignal(str)  # app path
    categoryChanged = pyqtSignal(str)  # new category

    def __init__(self, partition_data, data_manager: Optional[DataManager] = None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.data = partition_data.get('data', {"默认": []})
        self.launch_count = partition_data.get('launch_count', {})
        self.current_category = "默认"
        self.exe_items = {}
        self.search_text = ''
        self.tag_cloud_selected = None
        self.data_manager = data_manager

        # Enhanced features
        self.batch_loader = None
        self.context_menu = None
        self.drag_indicator = None

        self.init_ui()
        self.setup_enhanced_features()

    def setup_enhanced_features(self):
        """Setup enhanced drag-and-drop and context menu features"""
        # Enhanced list widget
        if hasattr(self, 'listWidget'):
            self.listWidget.setAcceptDrops(True)
            self.listWidget.setDragDropMode(QListWidget.DragDrop)
            self.listWidget.setSelectionMode(QListWidget.ExtendedSelection)

            # Context menu
            self.listWidget.setContextMenuPolicy(Qt.CustomContextMenu)
            self.listWidget.customContextMenuRequested.connect(self.show_context_menu)

            # Enhanced drag and drop
            self.listWidget.dragEnterEvent = self.enhanced_drag_enter
            self.listWidget.dropEvent = self.enhanced_drop_event

    def show_context_menu(self, position):
        """Show context menu for list items"""
        item = self.listWidget.itemAt(position)
        if not item:
            return

        menu = QMenu(self)

        # Basic actions
        run_action = QAction("运行", self)
        run_action.triggered.connect(lambda: self.run_app(item))
        menu.addAction(run_action)

        menu.addSeparator()

        # Category actions
        if self.data_manager:
            categories = self.data_manager.get_all_categories()
            if len(categories) > 1:
                move_menu = menu.addMenu("移动到分类")
                for cat_name in categories:
                    if cat_name != self.current_category:
                        action = QAction(cat_name, self)
                        action.triggered.connect(lambda checked, cat=cat_name: self.move_to_category(item, cat))
                        move_menu.addAction(action)

        menu.addSeparator()

        # Favorite action
        if self.data_manager:
            exe_path = item.data(Qt.UserRole)
            metadata = self.data_manager.get_app_metadata(exe_path)
            if metadata:
                fav_text = "取消收藏" if metadata.is_favorite else "添加到收藏"
                fav_action = QAction(fav_text, self)
                fav_action.triggered.connect(lambda: self.toggle_favorite(item))
                menu.addAction(fav_action)

        # Remove action
        remove_action = QAction("删除", self)
        remove_action.triggered.connect(lambda: self.remove_app(item))
        menu.addAction(remove_action)

        menu.exec_(self.listWidget.mapToGlobal(position))

    def move_to_category(self, item, category):
        """Move item to different category"""
        exe_path = item.data(Qt.UserRole)
        if self.data_manager:
            self.data_manager.move_app(exe_path, category)

        # Update UI
        self.remove_exe_item(exe_path)
        self.appRemoved.emit(exe_path)

    def toggle_favorite(self, item):
        """Toggle favorite status of item"""
        exe_path = item.data(Qt.UserRole)
        if self.data_manager:
            is_favorite = self.data_manager.toggle_favorite(exe_path)
            # Update visual indicator
            self.update_item_appearance(item, is_favorite)

    def remove_app(self, item):
        """Remove application"""
        exe_path = item.data(Qt.UserRole)
        reply = QMessageBox.question(self, '确认删除',
                                   f'确定要删除 {os.path.basename(exe_path)} 吗？',
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            if self.data_manager:
                self.data_manager.remove_app(exe_path)
            self.remove_exe_item(exe_path)
            self.appRemoved.emit(exe_path)

    def remove_exe_item(self, exe_path):
        """Remove exe item from UI"""
        if exe_path in self.exe_items:
            item = self.exe_items[exe_path]
            row = self.listWidget.row(item)
            self.listWidget.takeItem(row)
            del self.exe_items[exe_path]

        # Remove from data
        for category, apps in self.data.items():
            if exe_path in apps:
                apps.remove(exe_path)

    def update_item_appearance(self, item, is_favorite=False):
        """Update item appearance based on status"""
        if is_favorite:
            # Add star indicator or change color
            text = item.text()
            if not text.startswith("★ "):
                item.setText("★ " + text)
        else:
            text = item.text()
            if text.startswith("★ "):
                item.setText(text[2:])

    def enhanced_drag_enter(self, event):
        """Enhanced drag enter with better feedback"""
        if event.mimeData().hasUrls():
            # Check if files are valid
            valid_files = []
            for url in event.mimeData().urls():
                path = url.toLocalFile()
                if path.lower().endswith(('.exe', '.lnk')):
                    valid_files.append(path)

            if valid_files:
                event.acceptProposedAction()
                # Show visual feedback
                self.show_drop_indicator(True)
                return

        event.ignore()

    def enhanced_drop_event(self, event):
        """Enhanced drop event with auto-categorization"""
        self.show_drop_indicator(False)

        if event.mimeData().hasUrls():
            added_apps = []
            for url in event.mimeData().urls():
                path = url.toLocalFile()
                exe_path = None

                if path.lower().endswith('.exe'):
                    exe_path = path
                elif path.lower().endswith('.lnk'):
                    exe_path = resolve_link(path)

                if exe_path and exe_path.lower().endswith('.exe'):
                    # Auto-categorize if data manager available
                    category = self.current_category
                    if self.data_manager:
                        auto_category = self.data_manager.auto_categorize_app(exe_path)
                        if auto_category != "默认":
                            category = auto_category

                    # Add app
                    if self.data_manager:
                        self.data_manager.add_app(exe_path, category, auto_categorized=(category != self.current_category))

                    # Add to current view if in same category
                    if category == self.current_category:
                        self.add_exe_item(exe_path)

                    added_apps.append(exe_path)
                    self.appAdded.emit(exe_path, category)
                else:
                    QMessageBox.warning(self, '添加失败', f'无法识别或解析：{os.path.basename(path)}')

            if added_apps:
                self.populate_tree()  # Refresh category tree
                self.tag_cloud.update()  # Refresh tag cloud

            event.acceptProposedAction()

    def show_drop_indicator(self, show):
        """Show/hide drop indicator"""
        if show:
            self.listWidget.setStyleSheet("""
                QListWidget {
                    border: 2px dashed #4CAF50;
                    background-color: rgba(76, 175, 80, 0.1);
                }
            """)
        else:
            self.listWidget.setStyleSheet("")

    def init_ui(self):
        main_layout = QVBoxLayout(self)
        # 搜索框
        search_layout = QHBoxLayout()
        self.search_edit = QLineEdit(self)
        self.search_edit.setPlaceholderText('搜索（支持拼音/模糊）...')
        self.search_edit.textChanged.connect(self.on_search)
        search_layout.addWidget(QLabel('搜索:'))
        search_layout.addWidget(self.search_edit)
        main_layout.addLayout(search_layout)
        # 标签云
        self.tag_cloud = TagCloudWidget(self.get_tag_counts, self.on_tag_clicked, self.get_current_tag, self)
        main_layout.addWidget(self.tag_cloud)
        # 分类树和应用列表
        hbox = QHBoxLayout()
        self.tree = QTreeWidget(self)
        self.tree.setHeaderLabel('分类')
        self.tree.itemClicked.connect(self.on_category_selected)
        hbox.addWidget(self.tree)
        vbox = QVBoxLayout()
        self.label = QLabel('拖拽EXE或快捷方式到此窗口生成快捷方式', self)
        vbox.addWidget(self.label)
        self.listWidget = QListWidget(self)
        self.listWidget.setDragDropMode(QListWidget.InternalMove)
        self.listWidget.itemDoubleClicked.connect(self.run_app)
        vbox.addWidget(self.listWidget)
        hbox.addLayout(vbox)
        main_layout.addLayout(hbox)
        self.setLayout(main_layout)
        self.populate_tree()
        self.refresh_app_list()

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            for url in event.mimeData().urls():
                path = url.toLocalFile()
                if path.lower().endswith('.exe') or path.lower().endswith('.lnk'):
                    event.accept()
                    return
        event.ignore()

    def dropEvent(self, event):
        for url in event.mimeData().urls():
            path = url.toLocalFile()
            exe_path = None
            if path.lower().endswith('.exe'):
                exe_path = path
            elif path.lower().endswith('.lnk'):
                exe_path = resolve_link(path)
            if exe_path and exe_path.lower().endswith('.exe'):
                self.add_exe_item(exe_path)
                if exe_path not in self.data.setdefault(self.current_category, []):
                    self.data[self.current_category].append(exe_path)
            else:
                QMessageBox.warning(self, '添加失败', f'无法识别或解析：{os.path.basename(path)}')

    def get_tag_counts(self):
        return {k: len(v) for k, v in self.data.items()}

    def on_tag_clicked(self, tag):
        self.current_category = tag
        self.tag_cloud_selected = tag
        self.refresh_app_list()
        self.tag_cloud.update()
        items = self.tree.findItems(tag, Qt.MatchRecursive | Qt.MatchExactly)
        if items:
            self.tree.setCurrentItem(items[0])

    def get_current_tag(self):
        return self.current_category

    def on_search(self, text):
        self.search_text = text.strip()
        self.refresh_app_list()

    def match_search(self, exe_path):
        if not self.search_text:
            return True
        name = exe_path.lower()
        py = ''.join([s[0] for s in lazy_pinyin(name)])
        py_full = ''.join(lazy_pinyin(name))
        st = self.search_text.lower()
        return (st in name or st in py or st in py_full)

    def populate_tree(self):
        self.tree.clear()
        for k in self.data.keys():
            self.tree.addTopLevelItem(QTreeWidgetItem([k]))
        self.tree.expandAll()

    def on_category_selected(self, item, col):
        self.current_category = item.text(0)
        self.tag_cloud_selected = self.current_category
        self.refresh_app_list()
        self.tag_cloud.update()

    def refresh_app_list(self):
        self.listWidget.clear()
        self.exe_items.clear()
        exes = self.data.get(self.current_category, [])
        for exe_path in exes:
            self.add_exe_item(exe_path, refresh=True)

    def add_exe_item(self, exe_path, refresh=False):
        if exe_path in self.exe_items:
            return
        item = QListWidgetItem(exe_path.split(os.sep)[-1])
        item.setData(Qt.UserRole, exe_path)
        self.listWidget.addItem(item)
        self.exe_items[exe_path] = item
        def set_icon(path, pixmap):
            if path in self.exe_items:
                self.exe_items[path].setIcon(QIcon(pixmap))
        thread = IconLoaderThread(exe_path)
        thread.iconLoaded.connect(set_icon)
        thread.start()
        if not hasattr(self, '_icon_threads'):
            self._icon_threads = []
        self._icon_threads.append(thread)
        if not refresh and exe_path not in self.data.setdefault(self.current_category, []):
            self.data[self.current_category].append(exe_path)

    def run_app(self, item):
        exe_path = item.data(Qt.UserRole)
        try:
            import subprocess
            subprocess.Popen([exe_path])
            self.launch_count[exe_path] = self.launch_count.get(exe_path, 0) + 1
        except Exception as e:
            QMessageBox.warning(self, '启动失败', str(e)) 