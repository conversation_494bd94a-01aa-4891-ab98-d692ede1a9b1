import os
import json
import shutil
import datetime
import time
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict, field

CONFIG_FILE = 'config.json'
BACKUP_DIR = 'resources/backup'
METADATA_FILE = 'app_metadata.json'

@dataclass
class AppMetadata:
    """Enhanced metadata for applications"""
    path: str
    name: str
    category: str
    launch_count: int = 0
    last_launched: Optional[float] = None
    date_added: Optional[float] = None
    tags: Optional[List[str]] = None
    is_favorite: bool = False
    auto_categorized: bool = False
    file_size: Optional[int] = None
    version: Optional[str] = None
    description: Optional[str] = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.date_added is None:
            self.date_added = time.time()

@dataclass
class CategoryInfo:
    """Enhanced category information"""
    name: str
    color: Optional[str] = None
    icon: Optional[str] = None
    description: Optional[str] = None
    auto_rules: Optional[List[str]] = None
    sort_order: int = 0

    def __post_init__(self):
        if self.auto_rules is None:
            self.auto_rules = []

class DataManager:
    def __init__(self):
        self.config = {}
        self.app_metadata: Dict[str, AppMetadata] = {}
        self.categories: Dict[str, CategoryInfo] = {}
        self.load_config()
        self.load_metadata()
        self._ensure_default_categories()

    def _ensure_default_categories(self):
        """Ensure default categories exist"""
        default_categories = {
            "默认": CategoryInfo("默认", "#808080", None, "默认分类"),
            "开发工具": CategoryInfo("开发工具", "#4CAF50", None, "编程和开发相关工具",
                                ["*visual studio*", "*vscode*", "*intellij*", "*eclipse*", "*git*"]),
            "办公软件": CategoryInfo("办公软件", "#2196F3", None, "办公和文档处理软件",
                                ["*office*", "*word*", "*excel*", "*powerpoint*", "*wps*"]),
            "娱乐游戏": CategoryInfo("娱乐游戏", "#FF9800", None, "游戏和娱乐软件",
                                ["*game*", "*steam*", "*origin*", "*uplay*"]),
            "系统工具": CategoryInfo("系统工具", "#9C27B0", None, "系统管理和维护工具",
                                ["*system*", "*admin*", "*manager*", "*cleaner*"]),
            "网络通讯": CategoryInfo("网络通讯", "#00BCD4", None, "网络和通讯软件",
                                ["*browser*", "*chrome*", "*firefox*", "*qq*", "*wechat*"])
        }

        for name, info in default_categories.items():
            if name not in self.categories:
                self.categories[name] = info

    def load_config(self):
        """Load main configuration"""
        if os.path.exists(CONFIG_FILE):
            try:
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"Error loading config: {e}")
                self.config = {}
        else:
            self.config = {}

        # Ensure basic structure
        if 'partitions' not in self.config:
            self.config['partitions'] = {'默认': {'data': {'默认': []}, 'launch_count': {}}}

    def load_metadata(self):
        """Load application metadata"""
        if os.path.exists(METADATA_FILE):
            try:
                with open(METADATA_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # Load app metadata
                    for path, meta_dict in data.get('apps', {}).items():
                        self.app_metadata[path] = AppMetadata(**meta_dict)
                    # Load categories
                    for name, cat_dict in data.get('categories', {}).items():
                        self.categories[name] = CategoryInfo(**cat_dict)
            except (json.JSONDecodeError, IOError) as e:
                print(f"Error loading metadata: {e}")

    def save_config(self):
        """Save main configuration"""
        try:
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            self.auto_backup()
        except IOError as e:
            print(f"Error saving config: {e}")

    def save_metadata(self):
        """Save application metadata"""
        try:
            data = {
                'apps': {path: asdict(meta) for path, meta in self.app_metadata.items()},
                'categories': {name: asdict(cat) for name, cat in self.categories.items()}
            }
            with open(METADATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except IOError as e:
            print(f"Error saving metadata: {e}")

    def auto_backup(self):
        """Create automatic backup"""
        try:
            if not os.path.exists(BACKUP_DIR):
                os.makedirs(BACKUP_DIR)
            now = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

            # Backup config
            backup_file = os.path.join(BACKUP_DIR, f'windesktop_backup_{now}.json')
            shutil.copy(CONFIG_FILE, backup_file)

            # Backup metadata if exists
            if os.path.exists(METADATA_FILE):
                metadata_backup = os.path.join(BACKUP_DIR, f'metadata_backup_{now}.json')
                shutil.copy(METADATA_FILE, metadata_backup)

            # Clean old backups (keep last 10)
            self._cleanup_old_backups()
        except IOError as e:
            print(f"Error creating backup: {e}")

    def _cleanup_old_backups(self):
        """Remove old backup files, keeping only the most recent 10"""
        try:
            backup_files = []
            for file in os.listdir(BACKUP_DIR):
                if file.startswith('windesktop_backup_'):
                    backup_files.append(os.path.join(BACKUP_DIR, file))

            backup_files.sort(key=os.path.getmtime, reverse=True)
            for old_backup in backup_files[10:]:  # Keep only 10 most recent
                os.remove(old_backup)
        except OSError:
            pass  # Ignore cleanup errors

    # Enhanced App Management Methods
    def add_app(self, app_path: str, category: str = "默认", auto_categorized: bool = False) -> bool:
        """Add an application with enhanced metadata"""
        try:
            if not os.path.exists(app_path):
                return False

            # Create metadata
            app_name = os.path.basename(app_path)
            metadata = AppMetadata(
                path=app_path,
                name=app_name,
                category=category,
                auto_categorized=auto_categorized,
                file_size=os.path.getsize(app_path) if os.path.exists(app_path) else None
            )

            self.app_metadata[app_path] = metadata

            # Add to config structure for backward compatibility
            partitions = self.config.setdefault('partitions', {})
            default_partition = partitions.setdefault('默认', {'data': {}, 'launch_count': {}})
            category_apps = default_partition['data'].setdefault(category, [])

            if app_path not in category_apps:
                category_apps.append(app_path)

            self.save_config()
            self.save_metadata()
            return True
        except Exception as e:
            print(f"Error adding app {app_path}: {e}")
            return False

    def remove_app(self, app_path: str) -> bool:
        """Remove an application and its metadata"""
        try:
            # Remove from metadata
            if app_path in self.app_metadata:
                del self.app_metadata[app_path]

            # Remove from config structure
            for partition_name, partition_data in self.config.get('partitions', {}).items():
                for category, apps in partition_data.get('data', {}).items():
                    if app_path in apps:
                        apps.remove(app_path)

                # Remove from launch count
                if app_path in partition_data.get('launch_count', {}):
                    del partition_data['launch_count'][app_path]

            self.save_config()
            self.save_metadata()
            return True
        except Exception as e:
            print(f"Error removing app {app_path}: {e}")
            return False

    def move_app(self, app_path: str, new_category: str) -> bool:
        """Move an application to a different category"""
        try:
            # Update metadata
            if app_path in self.app_metadata:
                self.app_metadata[app_path].category = new_category

            # Update config structure
            for partition_data in self.config.get('partitions', {}).values():
                for category, apps in partition_data.get('data', {}).items():
                    if app_path in apps:
                        apps.remove(app_path)
                        break

                # Add to new category
                partition_data['data'].setdefault(new_category, []).append(app_path)

            self.save_config()
            self.save_metadata()
            return True
        except Exception as e:
            print(f"Error moving app {app_path}: {e}")
            return False

    def record_app_launch(self, app_path: str):
        """Record application launch for usage tracking"""
        try:
            current_time = time.time()

            # Update metadata
            if app_path in self.app_metadata:
                metadata = self.app_metadata[app_path]
                metadata.launch_count += 1
                metadata.last_launched = current_time

            # Update config structure for backward compatibility
            for partition_data in self.config.get('partitions', {}).values():
                launch_count = partition_data.setdefault('launch_count', {})
                launch_count[app_path] = launch_count.get(app_path, 0) + 1

            self.save_metadata()
        except Exception as e:
            print(f"Error recording launch for {app_path}: {e}")

    def get_app_metadata(self, app_path: str) -> Optional[AppMetadata]:
        """Get metadata for an application"""
        return self.app_metadata.get(app_path)

    def get_recent_apps(self, limit: int = 10) -> List[AppMetadata]:
        """Get recently launched applications"""
        recent_apps = [meta for meta in self.app_metadata.values() if meta.last_launched]
        recent_apps.sort(key=lambda x: x.last_launched or 0, reverse=True)
        return recent_apps[:limit]

    def get_most_used_apps(self, limit: int = 10) -> List[AppMetadata]:
        """Get most frequently used applications"""
        used_apps = [meta for meta in self.app_metadata.values() if meta.launch_count > 0]
        used_apps.sort(key=lambda x: x.launch_count, reverse=True)
        return used_apps[:limit]

    def get_favorite_apps(self) -> List[AppMetadata]:
        """Get favorite applications"""
        return [meta for meta in self.app_metadata.values() if meta.is_favorite]

    def toggle_favorite(self, app_path: str) -> bool:
        """Toggle favorite status of an application"""
        if app_path in self.app_metadata:
            self.app_metadata[app_path].is_favorite = not self.app_metadata[app_path].is_favorite
            self.save_metadata()
            return self.app_metadata[app_path].is_favorite
        return False

    # Category Management Methods
    def add_category(self, name: str, color: Optional[str] = None,
                    description: Optional[str] = None, auto_rules: Optional[List[str]] = None) -> bool:
        """Add a new category"""
        try:
            if name in self.categories:
                return False  # Category already exists

            self.categories[name] = CategoryInfo(
                name=name,
                color=color,
                description=description,
                auto_rules=auto_rules or []
            )

            # Add to config structure
            for partition_data in self.config.get('partitions', {}).values():
                partition_data['data'].setdefault(name, [])

            self.save_config()
            self.save_metadata()
            return True
        except Exception as e:
            print(f"Error adding category {name}: {e}")
            return False

    def remove_category(self, name: str, move_apps_to: str = "默认") -> bool:
        """Remove a category and move its apps to another category"""
        try:
            if name not in self.categories or name == "默认":
                return False  # Can't remove default category

            # Move all apps from this category
            apps_to_move = []
            for app_path, metadata in self.app_metadata.items():
                if metadata.category == name:
                    apps_to_move.append(app_path)

            for app_path in apps_to_move:
                self.move_app(app_path, move_apps_to)

            # Remove category
            del self.categories[name]

            # Remove from config structure
            for partition_data in self.config.get('partitions', {}).values():
                if name in partition_data.get('data', {}):
                    del partition_data['data'][name]

            self.save_config()
            self.save_metadata()
            return True
        except Exception as e:
            print(f"Error removing category {name}: {e}")
            return False

    def get_category_info(self, name: str) -> Optional[CategoryInfo]:
        """Get information about a category"""
        return self.categories.get(name)

    def get_all_categories(self) -> Dict[str, CategoryInfo]:
        """Get all categories"""
        return self.categories.copy()

    def update_category(self, name: str, **kwargs) -> bool:
        """Update category properties"""
        try:
            if name not in self.categories:
                return False

            category = self.categories[name]
            for key, value in kwargs.items():
                if hasattr(category, key):
                    setattr(category, key, value)

            self.save_metadata()
            return True
        except Exception as e:
            print(f"Error updating category {name}: {e}")
            return False

    # Auto-categorization Methods
    def auto_categorize_app(self, app_path: str) -> str:
        """Automatically categorize an application based on rules"""
        try:
            app_name = os.path.basename(app_path).lower()

            for category_name, category_info in self.categories.items():
                if category_info.auto_rules:
                    for rule in category_info.auto_rules:
                        # Simple wildcard matching
                        rule_pattern = rule.lower().replace('*', '')
                        if rule_pattern in app_name:
                            return category_name

            return "默认"  # Default category if no rules match
        except Exception as e:
            print(f"Error auto-categorizing {app_path}: {e}")
            return "默认"

    def run_auto_categorization(self) -> int:
        """Run auto-categorization on all uncategorized apps"""
        categorized_count = 0
        try:
            for app_path, metadata in self.app_metadata.items():
                if not metadata.auto_categorized and metadata.category == "默认":
                    new_category = self.auto_categorize_app(app_path)
                    if new_category != "默认":
                        self.move_app(app_path, new_category)
                        metadata.auto_categorized = True
                        categorized_count += 1

            if categorized_count > 0:
                self.save_config()
                self.save_metadata()
        except Exception as e:
            print(f"Error running auto-categorization: {e}")

        return categorized_count

    # Search and Filter Methods
    def search_apps(self, query: str, category: Optional[str] = None) -> List[AppMetadata]:
        """Search applications by name, tags, or description"""
        query = query.lower()
        results = []

        for metadata in self.app_metadata.values():
            if category and metadata.category != category:
                continue

            # Search in name
            if query in metadata.name.lower():
                results.append(metadata)
                continue

            # Search in tags
            if metadata.tags:
                for tag in metadata.tags:
                    if query in tag.lower():
                        results.append(metadata)
                        break

            # Search in description
            if metadata.description and query in metadata.description.lower():
                results.append(metadata)

        return results

    def get_apps_by_category(self, category: str) -> List[AppMetadata]:
        """Get all applications in a specific category"""
        return [meta for meta in self.app_metadata.values() if meta.category == category]