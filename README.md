# Windesktop 启动器

## 目录结构（重构后）

```
Windesktop/
  main.py                # 仅负责应用启动和主窗口调度
  ui/
    main_window.py       # 主窗口与布局
    partition_tab.py     # 分区Tab及其管理
    app_list_panel.py    # 应用列表与分类树
    tag_cloud.py         # 标签云控件
  core/
    icon_utils.py        # 图标提取与缓存
    data_manager.py      # 配置、备份、恢复、自动保存
    system_ops.py        # 系统集成功能（关机、重启、UWP等）
    async_utils.py       # 异步加载与线程管理
  resources/
    icon_cache/
    backup/
  config.json
  requirements.txt
  README.md
```

## 重构目标
- UI与业务逻辑分离，便于维护和扩展。
- 核心功能（图标、数据、系统操作、异步）单独成模块。
- 资源、缓存、备份等目录清晰。 