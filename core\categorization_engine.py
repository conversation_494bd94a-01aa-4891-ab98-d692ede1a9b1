"""
Smart Categorization Engine for Windesktop
Provides intelligent auto-categorization based on application type, usage patterns, and user preferences.
"""

import os
import re
import json
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass
from pathlib import Path

@dataclass
class CategoryRule:
    """Rule for categorizing applications"""
    name: str
    patterns: List[str]  # File name patterns
    paths: List[str]     # Installation path patterns
    keywords: List[str]  # Description/metadata keywords
    priority: int = 0    # Higher priority rules are checked first
    confidence: float = 1.0  # Confidence level (0.0 to 1.0)

class SmartCategorizationEngine:
    """Intelligent categorization engine for applications"""
    
    def __init__(self):
        self.rules: Dict[str, List[CategoryRule]] = {}
        self.user_patterns: Dict[str, List[str]] = {}
        self.usage_patterns: Dict[str, Dict[str, int]] = {}
        self.load_default_rules()
        self.load_user_patterns()
    
    def load_default_rules(self):
        """Load default categorization rules"""
        default_rules = {
            "开发工具": [
                CategoryRule(
                    name="IDE和编辑器",
                    patterns=["*visual*studio*", "*vscode*", "*code*", "*intellij*", "*eclipse*", 
                             "*sublime*", "*notepad++*", "*atom*", "*brackets*", "*vim*", "*emacs*"],
                    paths=["*microsoft*", "*jetbrains*", "*eclipse*", "*sublime*"],
                    keywords=["ide", "editor", "development", "programming", "code"],
                    priority=10,
                    confidence=0.95
                ),
                CategoryRule(
                    name="版本控制",
                    patterns=["*git*", "*svn*", "*mercurial*", "*tortoise*", "*sourcetree*"],
                    paths=["*git*", "*subversion*"],
                    keywords=["version control", "git", "svn", "repository"],
                    priority=9,
                    confidence=0.9
                ),
                CategoryRule(
                    name="数据库工具",
                    patterns=["*mysql*", "*postgres*", "*mongodb*", "*redis*", "*sqlite*", 
                             "*navicat*", "*dbeaver*", "*phpmyadmin*"],
                    paths=["*database*", "*sql*"],
                    keywords=["database", "sql", "mysql", "postgres", "mongodb"],
                    priority=8,
                    confidence=0.9
                )
            ],
            
            "办公软件": [
                CategoryRule(
                    name="Microsoft Office",
                    patterns=["*word*", "*excel*", "*powerpoint*", "*outlook*", "*onenote*", 
                             "*access*", "*publisher*", "*project*", "*visio*"],
                    paths=["*microsoft*office*", "*office*"],
                    keywords=["office", "word", "excel", "powerpoint", "microsoft"],
                    priority=10,
                    confidence=0.95
                ),
                CategoryRule(
                    name="WPS Office",
                    patterns=["*wps*", "*kingsoft*"],
                    paths=["*wps*", "*kingsoft*"],
                    keywords=["wps", "kingsoft", "office"],
                    priority=9,
                    confidence=0.9
                ),
                CategoryRule(
                    name="PDF工具",
                    patterns=["*adobe*reader*", "*acrobat*", "*foxit*", "*pdf*"],
                    paths=["*adobe*", "*foxit*"],
                    keywords=["pdf", "reader", "acrobat"],
                    priority=8,
                    confidence=0.85
                )
            ],
            
            "娱乐游戏": [
                CategoryRule(
                    name="游戏平台",
                    patterns=["*steam*", "*origin*", "*uplay*", "*epic*", "*battle.net*", 
                             "*gog*", "*launcher*"],
                    paths=["*steam*", "*origin*", "*ubisoft*", "*epic*", "*blizzard*"],
                    keywords=["game", "gaming", "launcher", "platform"],
                    priority=10,
                    confidence=0.95
                ),
                CategoryRule(
                    name="游戏",
                    patterns=["*game*", "*.exe"],
                    paths=["*games*", "*steam*", "*program files*games*"],
                    keywords=["game", "gaming", "play"],
                    priority=5,
                    confidence=0.7
                )
            ],
            
            "网络通讯": [
                CategoryRule(
                    name="浏览器",
                    patterns=["*chrome*", "*firefox*", "*edge*", "*safari*", "*opera*", 
                             "*brave*", "*vivaldi*", "*browser*"],
                    paths=["*google*", "*mozilla*", "*microsoft*edge*"],
                    keywords=["browser", "web", "internet"],
                    priority=10,
                    confidence=0.95
                ),
                CategoryRule(
                    name="即时通讯",
                    patterns=["*qq*", "*wechat*", "*skype*", "*discord*", "*telegram*", 
                             "*whatsapp*", "*slack*", "*teams*"],
                    paths=["*tencent*", "*microsoft*teams*", "*discord*"],
                    keywords=["chat", "messaging", "communication", "im"],
                    priority=9,
                    confidence=0.9
                ),
                CategoryRule(
                    name="下载工具",
                    patterns=["*thunder*", "*idm*", "*aria2*", "*qbittorrent*", "*utorrent*", 
                             "*download*"],
                    paths=["*thunder*", "*internet download manager*"],
                    keywords=["download", "torrent", "p2p"],
                    priority=8,
                    confidence=0.85
                )
            ],
            
            "系统工具": [
                CategoryRule(
                    name="系统维护",
                    patterns=["*ccleaner*", "*malwarebytes*", "*antivirus*", "*defender*", 
                             "*cleaner*", "*optimizer*", "*registry*"],
                    paths=["*system*", "*windows*", "*antivirus*"],
                    keywords=["system", "clean", "optimize", "antivirus", "security"],
                    priority=9,
                    confidence=0.9
                ),
                CategoryRule(
                    name="压缩工具",
                    patterns=["*winrar*", "*7zip*", "*zip*", "*archive*"],
                    paths=["*winrar*", "*7-zip*"],
                    keywords=["archive", "compress", "zip", "rar"],
                    priority=8,
                    confidence=0.9
                )
            ],
            
            "多媒体": [
                CategoryRule(
                    name="视频播放器",
                    patterns=["*vlc*", "*potplayer*", "*kmplayer*", "*media*player*", 
                             "*video*", "*player*"],
                    paths=["*videolan*", "*potplayer*"],
                    keywords=["video", "player", "media", "multimedia"],
                    priority=8,
                    confidence=0.85
                ),
                CategoryRule(
                    name="音频工具",
                    patterns=["*audacity*", "*foobar*", "*winamp*", "*music*", "*audio*"],
                    paths=["*audio*", "*music*"],
                    keywords=["audio", "music", "sound", "player"],
                    priority=7,
                    confidence=0.8
                ),
                CategoryRule(
                    name="图像处理",
                    patterns=["*photoshop*", "*gimp*", "*paint*", "*image*", "*photo*"],
                    paths=["*adobe*", "*gimp*"],
                    keywords=["image", "photo", "graphics", "edit"],
                    priority=8,
                    confidence=0.85
                )
            ]
        }
        
        self.rules = default_rules
    
    def load_user_patterns(self):
        """Load user-defined categorization patterns"""
        try:
            patterns_file = "user_categorization_patterns.json"
            if os.path.exists(patterns_file):
                with open(patterns_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.user_patterns = data.get('patterns', {})
                    self.usage_patterns = data.get('usage', {})
        except Exception as e:
            print(f"Error loading user patterns: {e}")
    
    def save_user_patterns(self):
        """Save user-defined categorization patterns"""
        try:
            patterns_file = "user_categorization_patterns.json"
            data = {
                'patterns': self.user_patterns,
                'usage': self.usage_patterns
            }
            with open(patterns_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving user patterns: {e}")
    
    def categorize_application(self, app_path: str, app_metadata: Optional[Dict] = None) -> Tuple[str, float]:
        """
        Categorize an application and return category name with confidence score
        
        Args:
            app_path: Path to the application executable
            app_metadata: Optional metadata about the application
            
        Returns:
            Tuple of (category_name, confidence_score)
        """
        app_name = os.path.basename(app_path).lower()
        app_dir = os.path.dirname(app_path).lower()
        
        # Extract metadata
        description = ""
        company = ""
        if app_metadata:
            description = app_metadata.get('description', '').lower()
            company = app_metadata.get('company', '').lower()
        
        best_category = "默认"
        best_confidence = 0.0
        
        # Check all category rules
        for category, rules in self.rules.items():
            for rule in sorted(rules, key=lambda r: r.priority, reverse=True):
                confidence = self._calculate_rule_confidence(
                    rule, app_name, app_dir, description, company
                )
                
                if confidence > best_confidence:
                    best_category = category
                    best_confidence = confidence
        
        # Check user patterns
        user_category, user_confidence = self._check_user_patterns(app_path)
        if user_confidence > best_confidence:
            best_category = user_category
            best_confidence = user_confidence
        
        # Apply usage pattern boost
        usage_boost = self._get_usage_pattern_boost(app_path, best_category)
        best_confidence = min(1.0, best_confidence + usage_boost)
        
        return best_category, best_confidence
    
    def _calculate_rule_confidence(self, rule: CategoryRule, app_name: str, 
                                 app_dir: str, description: str, company: str) -> float:
        """Calculate confidence score for a specific rule"""
        score = 0.0
        matches = 0
        total_checks = 0
        
        # Check filename patterns
        for pattern in rule.patterns:
            total_checks += 1
            if self._match_pattern(pattern.lower(), app_name):
                matches += 1
                score += 0.4  # High weight for filename matches
        
        # Check path patterns
        for pattern in rule.paths:
            total_checks += 1
            if self._match_pattern(pattern.lower(), app_dir):
                matches += 1
                score += 0.3  # Medium weight for path matches
        
        # Check keyword matches in description and company
        text_to_search = f"{description} {company}"
        for keyword in rule.keywords:
            total_checks += 1
            if keyword.lower() in text_to_search:
                matches += 1
                score += 0.2  # Lower weight for keyword matches
        
        # Normalize score and apply rule confidence
        if total_checks > 0:
            normalized_score = min(1.0, score)
            return normalized_score * rule.confidence
        
        return 0.0
    
    def _match_pattern(self, pattern: str, text: str) -> bool:
        """Match a pattern against text (supports wildcards)"""
        # Convert wildcard pattern to regex
        regex_pattern = pattern.replace('*', '.*')
        try:
            return bool(re.search(regex_pattern, text))
        except re.error:
            # Fallback to simple substring match
            return pattern in text
    
    def _check_user_patterns(self, app_path: str) -> Tuple[str, float]:
        """Check user-defined patterns"""
        app_name = os.path.basename(app_path).lower()
        
        for category, patterns in self.user_patterns.items():
            for pattern in patterns:
                if self._match_pattern(pattern.lower(), app_name):
                    return category, 0.95  # High confidence for user patterns
        
        return "默认", 0.0
    
    def _get_usage_pattern_boost(self, app_path: str, category: str) -> float:
        """Get confidence boost based on usage patterns"""
        app_usage = self.usage_patterns.get(app_path, {})
        category_count = app_usage.get(category, 0)
        total_count = sum(app_usage.values())
        
        if total_count > 0:
            ratio = category_count / total_count
            return min(0.2, ratio * 0.2)  # Max boost of 0.2
        
        return 0.0
    
    def learn_from_user_action(self, app_path: str, category: str):
        """Learn from user categorization actions"""
        # Update usage patterns
        if app_path not in self.usage_patterns:
            self.usage_patterns[app_path] = {}
        
        self.usage_patterns[app_path][category] = self.usage_patterns[app_path].get(category, 0) + 1
        
        # If user consistently categorizes similar apps, create a pattern
        app_name = os.path.basename(app_path).lower()
        self._update_user_patterns(app_name, category)
        
        self.save_user_patterns()
    
    def _update_user_patterns(self, app_name: str, category: str):
        """Update user patterns based on consistent categorization"""
        # Extract potential patterns from app name
        # This is a simple implementation - could be made more sophisticated
        if len(app_name) > 3:
            # Create pattern from first few characters
            pattern = app_name[:4] + "*"
            
            if category not in self.user_patterns:
                self.user_patterns[category] = []
            
            if pattern not in self.user_patterns[category]:
                self.user_patterns[category].append(pattern)
    
    def get_category_suggestions(self, app_path: str, app_metadata: Optional[Dict] = None) -> List[Tuple[str, float]]:
        """Get multiple category suggestions with confidence scores"""
        app_name = os.path.basename(app_path).lower()
        app_dir = os.path.dirname(app_path).lower()
        
        # Extract metadata
        description = ""
        company = ""
        if app_metadata:
            description = app_metadata.get('description', '').lower()
            company = app_metadata.get('company', '').lower()
        
        suggestions = []
        
        # Check all categories
        for category, rules in self.rules.items():
            max_confidence = 0.0
            for rule in rules:
                confidence = self._calculate_rule_confidence(
                    rule, app_name, app_dir, description, company
                )
                max_confidence = max(max_confidence, confidence)
            
            if max_confidence > 0.1:  # Only include if some confidence
                suggestions.append((category, max_confidence))
        
        # Sort by confidence
        suggestions.sort(key=lambda x: x[1], reverse=True)
        return suggestions[:5]  # Return top 5 suggestions
    
    def add_custom_rule(self, category: str, rule: CategoryRule):
        """Add a custom categorization rule"""
        if category not in self.rules:
            self.rules[category] = []
        
        self.rules[category].append(rule)
        self.rules[category].sort(key=lambda r: r.priority, reverse=True)
    
    def get_statistics(self) -> Dict:
        """Get categorization statistics"""
        stats = {
            'total_rules': sum(len(rules) for rules in self.rules.values()),
            'categories': list(self.rules.keys()),
            'user_patterns': len(self.user_patterns),
            'learned_apps': len(self.usage_patterns)
        }
        return stats
